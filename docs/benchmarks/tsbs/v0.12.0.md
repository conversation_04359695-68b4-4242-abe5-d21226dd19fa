# TSBS benchmark - v0.12.0

## Environment

### Amazon EC2

|         |                         |
|---------|-------------------------|
| Machine | c5d.2xlarge             |
| CPU     | 8 core                  |
| Memory  | 16GB                    |
| Disk    | 100GB (GP3)             |
| OS      | Ubuntu Server 24.04 LTS |

## Write performance

| Environment     | Ingest rate (rows/s) |
|-----------------|----------------------|
| EC2 c5d.2xlarge | 326839.28            |

## Query performance

| Query type            | EC2 c5d.2xlarge (ms) |
|-----------------------|----------------------|
| cpu-max-all-1         | 12.46                |
| cpu-max-all-8         | 24.20                |
| double-groupby-1      | 673.08               |
| double-groupby-5      | 963.99               |
| double-groupby-all    | 1330.05              |
| groupby-orderby-limit | 952.46               |
| high-cpu-1            | 5.08                 |
| high-cpu-all          | 4638.57              |
| lastpoint             | 591.02               |
| single-groupby-1-1-1  | 4.06                 |
| single-groupby-1-1-12 | 4.73                 |
| single-groupby-1-8-1  | 8.23                 |
| single-groupby-5-1-1  | 4.61                 |
| single-groupby-5-1-12 | 5.61                 |
| single-groupby-5-8-1  | 9.74                 |

