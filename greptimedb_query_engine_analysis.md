# GreptimeDB 查询引擎技术分析

## 概述

GreptimeDB 的查询引擎是一个基于 Apache DataFusion 构建的高性能分布式查询处理系统。它支持多种查询语言（SQL、PromQL），具备完整的查询优化、分布式执行和扩展能力。

## 架构设计

### 核心组件架构

```
┌─────────────────────────────────────────────────────────────┐
│                    Query Engine                             │
├─────────────────────────────────────────────────────────────┤
│  Parser Layer                                               │
│  ├── SQL Parser (GreptimeDB Dialect)                        │
│  ├── PromQL Parser                                          │
│  └── Log Query Parser                                       │
├─────────────────────────────────────────────────────────────┤
│  Logical Planning Layer                                     │
│  ├── DfLogicalPlanner                                       │
│  ├── Range Plan Rewriter                                    │
│  └── Extension Rules                                        │
├─────────────────────────────────────────────────────────────┤
│  Optimization Layer                                         │
│  ├── Logical Optimizer (DataFusion + Custom Rules)          │
│  ├── Physical Optimizer                                     │
│  └── Distribution Optimizer                                 │
├─────────────────────────────────────────────────────────────┤
│  Physical Planning Layer                                    │
│  ├── DataFusion Physical Planner                            │
│  ├── Distribution Extension Planner                         │
│  └── Custom Execution Plans                                 │
├─────────────────────────────────────────────────────────────┤
│  Execution Layer                                            │
│  ├── DatafusionQueryEngine                                  │
│  ├── Region Query Handler                                   │
│  └── Distributed Execution                                  │
└─────────────────────────────────────────────────────────────┘
```

## 核心实现分析

### 1. 查询引擎接口 (QueryEngine Trait)

查询引擎的核心接口定义了统一的查询处理能力：

```rust
#[async_trait]
pub trait QueryEngine: Send + Sync {
    fn as_any(&self) -> &dyn Any;
    fn planner(&self) -> Arc<dyn LogicalPlanner>;
    fn name(&self) -> &str;
    
    // 核心查询方法
    async fn describe(&self, plan: LogicalPlan, query_ctx: QueryContextRef) -> Result<DescribeResult>;
    async fn execute(&self, plan: LogicalPlan, query_ctx: QueryContextRef) -> Result<Output>;
    
    // 函数注册
    fn register_aggregate_function(&self, func: AggregateUDF);
    fn register_scalar_function(&self, func: ScalarFunctionFactory);
    
    // 数据访问
    fn read_table(&self, table: TableRef) -> Result<DataFrame>;
    fn engine_context(&self, query_ctx: QueryContextRef) -> QueryEngineContext;
    fn engine_state(&self) -> &QueryEngineState;
}
```

### 2. DataFusion 查询引擎实现

`DatafusionQueryEngine` 是主要的查询引擎实现，基于 Apache DataFusion：

#### 核心特性：
- **多语言支持**: SQL、PromQL、日志查询
- **分布式执行**: 支持跨区域的分布式查询
- **优化器集成**: 自定义优化规则 + DataFusion 优化器
- **插件系统**: 支持物理计划包装器和扩展

#### 查询执行流程：

```rust
async fn exec_query_plan(&self, plan: LogicalPlan, query_ctx: QueryContextRef) -> Result<Output> {
    let mut ctx = self.engine_context(query_ctx.clone());
    
    // 1. 创建物理计划（包含逻辑优化）
    let physical_plan = self.create_physical_plan(&mut ctx, &plan).await?;
    
    // 2. 物理计划优化
    let optimized_physical_plan = self.optimize_physical_plan(&mut ctx, physical_plan)?;
    
    // 3. 插件包装
    let physical_plan = if let Some(wrapper) = self.plugins.get::<PhysicalPlanWrapperRef>() {
        wrapper.wrap(optimized_physical_plan, query_ctx)
    } else {
        optimized_physical_plan
    };
    
    // 4. 执行并返回结果流
    Ok(Output::new(
        OutputData::Stream(self.execute_stream(&ctx, &physical_plan)?),
        OutputMeta::new_with_plan(physical_plan),
    ))
}
```

### 3. 逻辑计划器 (DfLogicalPlanner)

负责将查询语句转换为逻辑执行计划：

#### 主要功能：
- **SQL 到逻辑计划转换**: 使用 DataFusion 的 SqlToRel
- **PromQL 支持**: 通过 PromPlanner 处理 Prometheus 查询
- **范围查询重写**: RangePlanRewriter 处理时间范围查询
- **扩展规则应用**: 应用自定义的逻辑优化规则

```rust
async fn plan_sql(&self, stmt: &Statement, query_ctx: QueryContextRef) -> Result<LogicalPlan> {
    // 1. SQL 语句转换为 DataFusion 语句
    let df_stmt = stmt.try_into().context(SqlSnafu)?;
    
    // 2. 创建表提供者和上下文提供者
    let table_provider = DfTableSourceProvider::new(/* ... */);
    let context_provider = DfContextProviderAdapter::try_new(/* ... */).await?;
    
    // 3. SQL 转逻辑计划
    let sql_to_rel = SqlToRel::new_with_options(&context_provider, parser_options);
    let result = sql_to_rel.statement_to_plan(df_stmt).context(PlanSqlSnafu)?;
    
    // 4. 范围查询重写
    let plan = RangePlanRewriter::new(table_provider, query_ctx.clone())
        .rewrite(result).await?;
    
    // 5. 应用扩展优化规则
    let context = QueryEngineContext::new(self.session_state.clone(), query_ctx);
    let plan = self.engine_state.optimize_by_extension_rules(plan, &context)?;
    
    Ok(plan)
}
```

### 4. 查询引擎状态 (QueryEngineState)

管理查询引擎的全局状态和配置：

#### 核心组件：
- **DataFusion 会话上下文**: 管理 DataFusion 的配置和状态
- **目录管理器**: 访问表和模式信息
- **函数注册表**: 标量函数和聚合函数
- **优化规则**: 自定义的分析和优化规则
- **插件系统**: 支持功能扩展

#### 优化器配置：

```rust
// 分析器规则
let mut analyzer = Analyzer::new();
analyzer.rules.insert(0, Arc::new(TranscribeAtatRule));
analyzer.rules.insert(0, Arc::new(StringNormalizationRule));
analyzer.rules.insert(0, Arc::new(CountWildcardToTimeIndexRule));

// 逻辑优化器规则
let mut optimizer = Optimizer::new();
optimizer.rules.push(Arc::new(ScanHintRule));

// 物理优化器规则
let mut physical_optimizer = PhysicalOptimizer::new();
physical_optimizer.rules.insert(0, Arc::new(ParallelizeScan));
physical_optimizer.rules.insert(1, Arc::new(PassDistribution));
physical_optimizer.rules.push(Arc::new(WindowedSortPhysicalRule));
physical_optimizer.rules.push(Arc::new(RemoveDuplicate));
```

## 分布式查询处理

### 1. 分布式计划器 (DistPlannerAnalyzer)

将单机查询计划转换为分布式执行计划：

- **MergeScan 计划**: 将多个区域的扫描操作合并
- **分布式聚合**: 支持跨区域的聚合操作
- **数据分布感知**: 根据数据分布优化查询计划

### 2. MergeScan 执行计划详细实现

`MergeScanExec` 是分布式查询的核心执行算子，负责协调多个区域的查询执行：

#### 核心数据结构：

<augment_code_snippet path="src/query/src/dist_plan/merge_scan.rs" mode="EXCERPT">
````rust
pub struct MergeScanExec {
    table: TableName,                    // 目标表名
    regions: Vec<RegionId>,              // 需要查询的区域列表
    plan: LogicalPlan,                   // 要在每个区域执行的逻辑计划
    schema: SchemaRef,                   // 结果模式
    arrow_schema: ArrowSchemaRef,        // Arrow 格式的模式
    region_query_handler: RegionQueryHandlerRef,  // 区域查询处理器
    metric: ExecutionPlanMetricsSet,     // 执行指标
    properties: PlanProperties,          // 计划属性
    sub_stage_metrics: Arc<Mutex<HashMap<RegionId, RecordBatchMetrics>>>,  // 子阶段指标
    query_ctx: QueryContextRef,          // 查询上下文
    target_partition: usize,             // 目标分区数
    partition_cols: Vec<String>,         // 分区列
}
````
</augment_code_snippet>

#### 分布式查询执行流程：

<augment_code_snippet path="src/query/src/dist_plan/merge_scan.rs" mode="EXCERPT">
````rust
pub fn to_stream(
    &self,
    context: Arc<TaskContext>,
    partition: usize,
) -> Result<SendableRecordBatchStream> {
    // 1. 准备执行状态
    let regions = self.regions.clone();
    let region_query_handler = self.region_query_handler.clone();
    let metric = MergeScanMetric::new(&self.metric);
    let schema = self.schema.clone();
    let query_ctx = self.query_ctx.clone();
    let plan = self.plan.clone();
    let target_partition = self.target_partition;

    // 2. 创建异步流处理器
    let stream = stream! {
        // 3. 遍历分配给当前分区的区域
        for region_id in regions
            .iter()
            .skip(partition)
            .step_by(target_partition)
            .copied()
        {
            // 4. 构建区域查询请求
            let request = QueryRequest {
                header: Some(RegionRequestHeader {
                    tracing_context: tracing_context.to_w3c(),
                    dbname: dbname.clone(),
                    query_context: Some(query_ctx.as_ref().into()),
                }),
                region_id,
                plan: plan.clone(),
            };

            // 5. 执行区域查询
            let mut stream = region_query_handler
                .do_get(read_preference, request)
                .await?;

            // 6. 处理查询结果流
            while let Some(batch) = stream.next().await {
                let batch = batch?;
                // 重构批次以匹配目标模式
                let batch = RecordBatch::new(schema.clone(), batch.columns().iter().cloned())?;
                metric.record_output_batch_rows(batch.num_rows());

                // 收集指标信息
                if let Some(metrics) = stream.metrics() {
                    let mut sub_stage_metrics = sub_stage_metrics_moved.lock().unwrap();
                    sub_stage_metrics.insert(region_id, metrics);
                }

                yield Ok(batch);
            }
        }
    };

    Ok(Box::pin(RecordBatchStreamWrapper::new(schema, stream)))
}
````
</augment_code_snippet>

### 3. 分布式扩展计划器

`DistExtensionPlanner` 负责将逻辑的 MergeScan 计划转换为物理执行计划：

<augment_code_snippet path="src/query/src/dist_plan/planner.rs" mode="EXCERPT">
````rust
#[async_trait]
impl ExtensionPlanner for DistExtensionPlanner {
    async fn plan_extension(
        &self,
        planner: &dyn PhysicalPlanner,
        node: &dyn UserDefinedLogicalNode,
        _logical_inputs: &[&LogicalPlan],
        _physical_inputs: &[Arc<dyn ExecutionPlan>],
        session_state: &SessionState,
    ) -> Result<Option<Arc<dyn ExecutionPlan>>> {
        // 1. 检查是否为 MergeScan 节点
        let Some(merge_scan) = node.as_any().downcast_ref::<MergeScanLogicalPlan>() else {
            return Ok(None);
        };

        let input_plan = merge_scan.input();

        // 2. 处理占位符节点
        if merge_scan.is_placeholder() {
            return self.fallback_to_local_execution(planner, input_plan, session_state).await;
        }

        // 3. 提取表名信息
        let Some(table_name) = Self::extract_full_table_name(input_plan)? else {
            return self.fallback_to_local_execution(planner, input_plan, session_state).await;
        };

        // 4. 获取表的区域信息
        let Ok(regions) = self.get_regions(&table_name).await else {
            return self.fallback_to_local_execution(planner, input_plan, session_state).await;
        };

        // 5. 创建 MergeScan 物理执行计划
        let schema = input_plan.schema().as_ref().into();
        let query_ctx = session_state.config().get_extension().unwrap_or_else(QueryContext::arc);
        let merge_scan_plan = MergeScanExec::new(
            session_state,
            table_name,
            regions,
            input_plan.clone(),
            &schema,
            self.region_query_handler.clone(),
            query_ctx,
            session_state.config().target_partitions(),
            merge_scan.partition_cols().to_vec(),
        )?;

        Ok(Some(Arc::new(merge_scan_plan) as _))
    }
}
````
</augment_code_snippet>

### 4. 区域查询处理器

`RegionQueryHandler` 负责实际的区域查询执行：

<augment_code_snippet path="src/query/src/region_query.rs" mode="EXCERPT">
````rust
#[async_trait]
pub trait RegionQueryHandler: Send + Sync {
    /// 执行区域查询
    async fn do_get(
        &self,
        read_preference: ReadPreference,  // 读取偏好（主/从）
        request: QueryRequest,            // 查询请求
    ) -> Result<SendableRecordBatchStream>;
}

pub type RegionQueryHandlerRef = Arc<dyn RegionQueryHandler>;
````
</augment_code_snippet>

#### 前端区域查询处理器实现：

<augment_code_snippet path="src/frontend/src/instance/region_query.rs" mode="EXCERPT">
````rust
#[async_trait]
impl RegionQueryHandler for FrontendRegionQueryHandler {
    async fn do_get(
        &self,
        read_preference: ReadPreference,
        request: QueryRequest,
    ) -> QueryResult<SendableRecordBatchStream> {
        // 1. 根据区域 ID 和读取偏好选择目标节点
        let region_id = request.region_id;
        let target_node = self.select_target_node(region_id, read_preference).await?;

        // 2. 将逻辑计划序列化为 Substrait 格式
        let serialized_plan = self.serialize_logical_plan(&request.plan)?;

        // 3. 构建 gRPC 查询请求
        let grpc_request = api::v1::region::QueryRequest {
            header: request.header,
            region_id: region_id.as_u64(),
            plan: serialized_plan,
        };

        // 4. 发送查询请求到目标节点
        let response_stream = target_node.query(grpc_request).await?;

        // 5. 将响应流转换为 RecordBatch 流
        Ok(self.convert_response_stream(response_stream))
    }
}
````
</augment_code_snippet>

### 5. 分布式查询优化

#### 分区策略优化：

- **区域分区**: 根据区域数量和查询并行度分配分区
- **负载均衡**: 平衡各分区的查询负载
- **本地性优化**: 优先查询本地区域

#### 网络优化：

- **批量传输**: 批量传输查询结果减少网络开销
- **压缩**: 支持查询结果的压缩传输
- **流式处理**: 流式传输避免大结果集的内存积累

#### 容错机制：

- **重试策略**: 自动重试失败的区域查询
- **部分失败处理**: 容忍部分区域查询失败
- **超时控制**: 设置合理的查询超时时间

## 优化器系统

### 1. 自定义优化规则

GreptimeDB 实现了多个专门的优化规则：

#### 逻辑优化规则：
- **TypeConversionRule**: 类型转换优化
- **StringNormalizationRule**: 字符串标准化
- **CountWildcardToTimeIndexRule**: COUNT(*) 查询优化
- **ScanHintRule**: 扫描提示优化

#### 物理优化规则：
- **ParallelizeScan**: 扫描并行化
- **PassDistribution**: 分布要求传递
- **WindowedSortPhysicalRule**: 窗口排序优化
- **RemoveDuplicate**: 重复节点移除

### 2. 扫描并行化优化详细实现

`ParallelizeScan` 规则是 GreptimeDB 中最重要的物理优化规则之一，负责将单个区域扫描分解为多个并行分区：

<augment_code_snippet path="src/query/src/optimizer/parallelize_scan.rs" mode="EXCERPT">
````rust
impl PhysicalOptimizerRule for ParallelizeScan {
    fn optimize(
        &self,
        plan: Arc<dyn ExecutionPlan>,
        config: &ConfigOptions,
    ) -> Result<Arc<dyn ExecutionPlan>> {
        Self::do_optimize(plan, config)
    }

    fn name(&self) -> &str {
        "parallelize_scan"
    }
}

impl ParallelizeScan {
    fn do_optimize(
        plan: Arc<dyn ExecutionPlan>,
        config: &ConfigOptions,
    ) -> Result<Arc<dyn ExecutionPlan>> {
        let mut first_order_expr = None;

        let result = plan.transform_down(|plan| {
            // 1. 检测排序执行计划，保存排序表达式
            if let Some(sort_exec) = plan.as_any().downcast_ref::<SortExec>() {
                first_order_expr = sort_exec.expr().first().cloned();
            }
            // 2. 检测区域扫描执行计划
            else if let Some(region_scan_exec) = plan.as_any().downcast_ref::<RegionScanExec>() {
                let expected_partition_num = config.execution.target_partitions;

                // 跳过已经设置分区的扫描
                if region_scan_exec.is_partition_set() {
                    return Ok(Transformed::no(plan));
                }

                // 3. 获取分区范围并分配给各个分区
                let ranges = region_scan_exec.get_partition_ranges();
                let total_range_num = ranges.len();
                let mut partition_ranges = Self::assign_partition_range(ranges, expected_partition_num);

                // 4. 根据排序表达式优化分区内的范围顺序
                if let Some(order_expr) = &first_order_expr && order_expr.options.descending {
                    // 降序排序：按结束时间倒序排列
                    for ranges in partition_ranges.iter_mut() {
                        ranges.sort_by(|a, b| b.end.cmp(&a.end));
                    }
                } else {
                    // 升序排序：按开始时间正序排列
                    for ranges in partition_ranges.iter_mut() {
                        ranges.sort_by(|a, b| a.start.cmp(&b.start));
                    }
                }

                // 5. 创建新的并行化扫描执行计划
                let new_exec = region_scan_exec
                    .with_new_partitions(partition_ranges, expected_partition_num)?;
                return Ok(Transformed::yes(Arc::new(new_exec)));
            }
            Ok(Transformed::no(plan))
        })?;

        Ok(result.data)
    }

    /// 将分区范围分配给各个分区
    /// 当前根据行数分配，使每个分区具有相似的行数
    fn assign_partition_range(
        mut ranges: Vec<PartitionRange>,
        expected_partition_num: usize,
    ) -> Vec<Vec<PartitionRange>> {
        if ranges.is_empty() {
            return vec![vec![]; expected_partition_num];
        }

        if ranges.len() == 1 {
            let mut vec = vec![vec![]; expected_partition_num];
            vec[0] = ranges;
            return vec;
        }

        // 使用优先队列进行负载均衡分配
        let mut heap = BinaryHeap::new();
        for i in 0..expected_partition_num {
            heap.push(PartitionInfo::new(i));
        }

        // 按行数降序排序范围
        ranges.sort_by(|a, b| b.num_rows().cmp(&a.num_rows()));

        // 贪心分配：总是分配给当前行数最少的分区
        for range in ranges {
            if let Some(mut partition_info) = heap.pop() {
                partition_info.add_range(range);
                heap.push(partition_info);
            }
        }

        // 构建结果
        let mut result = vec![vec![]; expected_partition_num];
        while let Some(partition_info) = heap.pop() {
            result[partition_info.partition_id] = partition_info.ranges;
        }

        result
    }
}
````
</augment_code_snippet>

### 3. COUNT(*) 查询优化

`CountWildcardToTimeIndexRule` 是专门针对时间序列数据的 COUNT(*) 查询优化：

<augment_code_snippet path="src/query/src/optimizer/count_wildcard.rs" mode="EXCERPT">
````rust
/// 替代 DataFusion 的 CountWildcardRule
/// 优先使用时间索引进行 COUNT(*) 查询，因为读取时间索引比主键更快
#[derive(Debug)]
pub struct CountWildcardToTimeIndexRule;

impl AnalyzerRule for CountWildcardToTimeIndexRule {
    fn name(&self) -> &str {
        "count_wildcard_to_time_index_rule"
    }

    fn analyze(
        &self,
        plan: LogicalPlan,
        _config: &datafusion::config::ConfigOptions,
    ) -> DataFusionResult<LogicalPlan> {
        plan.transform_down_with_subqueries(&Self::analyze_internal).data()
    }
}

impl CountWildcardToTimeIndexRule {
    fn analyze_internal(plan: LogicalPlan) -> DataFusionResult<Transformed<LogicalPlan>> {
        let name_preserver = NamePreserver::new(&plan);

        // 1. 尝试查找时间索引列
        let new_arg = if let Some(time_index) = Self::try_find_time_index_col(&plan) {
            vec![col(time_index)]  // 使用时间索引列
        } else {
            vec![lit(COUNT_STAR_EXPANSION)]  // 使用默认展开
        };

        // 2. 转换 COUNT(*) 表达式
        let new_exprs = plan
            .expressions()
            .into_iter()
            .map(|expr| {
                expr.transform(&|expr| {
                    match &expr {
                        // 处理聚合函数中的 COUNT(*)
                        Expr::AggregateFunction(AggregateFunction { func, args, .. })
                            if Self::is_count_star_aggregate(&expr) => {
                            Ok(Transformed::yes(Expr::AggregateFunction(
                                AggregateFunction::new_udf(func.clone(), new_arg.clone(), ...)
                            )))
                        }
                        // 处理窗口函数中的 COUNT(*)
                        Expr::WindowFunction(window_func)
                            if Self::is_count_star_window_aggregate(window_func) => {
                            Ok(Transformed::yes(Expr::WindowFunction(WindowFunction {
                                args: new_arg.clone(),
                                ..window_func.clone()
                            })))
                        }
                        _ => Ok(Transformed::no(expr)),
                    }
                })
            })
            .collect::<Result<Vec<_>, _>>()?;

        // 3. 重构逻辑计划
        let inputs = plan.inputs().into_iter().cloned().collect();
        let new_plan = plan.with_new_exprs(new_exprs, inputs)?;
        name_preserver.save_and_restore(new_plan).map(Transformed::yes)
    }

    /// 尝试查找时间索引列
    fn try_find_time_index_col(plan: &LogicalPlan) -> Option<Column> {
        let mut finder = TimeIndexFinder::default();
        plan.visit(&mut finder).unwrap();
        let col = finder.into_column();

        // 验证时间索引列在当前计划中是否有效
        if let Some(col) = &col {
            let mut is_valid = false;
            for input in plan.inputs() {
                if input.schema().has_column(col) {
                    is_valid = true;
                    break;
                }
            }
            if !is_valid {
                return None;
            }
        }

        col
    }
}
````
</augment_code_snippet>

### 4. 类型转换优化

`TypeConversionRule` 负责在查询计划中进行智能的类型转换：

<augment_code_snippet path="src/query/src/optimizer/type_conversion.rs" mode="EXCERPT">
````rust
/// 类型转换规则根据对应列的数据类型将逻辑计划中的某些字面值转换为其他类型
/// 具体包括：
/// - 时间戳的字符串字面值转换为 TimestampMillis
/// - 布尔值的字符串字面值转换为 Boolean
pub struct TypeConversionRule;

impl ExtensionAnalyzerRule for TypeConversionRule {
    fn analyze(
        &self,
        plan: LogicalPlan,
        ctx: &QueryEngineContext,
        _config: &ConfigOptions,
    ) -> Result<LogicalPlan> {
        plan.transform(&|plan| match plan {
            // 1. 处理过滤条件
            LogicalPlan::Filter(filter) => {
                let mut converter = TypeConverter {
                    schema: filter.input.schema().clone(),
                    query_ctx: ctx.query_ctx(),
                };
                let rewritten = filter.predicate.clone().rewrite(&mut converter)?.data;
                Ok(Transformed::yes(LogicalPlan::Filter(Filter::try_new(
                    rewritten,
                    filter.input,
                )?)))
            }
            // 2. 处理其他计划节点
            LogicalPlan::Projection { .. }
            | LogicalPlan::Window { .. }
            | LogicalPlan::Aggregate { .. } => {
                let mut converter = TypeConverter {
                    schema: plan.schema().clone(),
                    query_ctx: ctx.query_ctx(),
                };
                let inputs = plan.inputs().into_iter().cloned().collect::<Vec<_>>();
                let expr = plan
                    .expressions_consider_join()
                    .into_iter()
                    .map(|e| e.rewrite(&mut converter).map(|x| x.data))
                    .collect::<Result<Vec<_>>>()?;

                plan.with_new_exprs(expr, inputs).map(Transformed::yes)
            }
            _ => Ok(Transformed::no(plan)),
        })
        .map(|x| x.data)
    }
}

struct TypeConverter {
    schema: DFSchemaRef,
    query_ctx: QueryContextRef,
}

impl TreeNodeRewriter for TypeConverter {
    type Node = Expr;

    fn f_up(&mut self, expr: Expr) -> Result<Transformed<Expr>> {
        match expr {
            // 处理二元表达式的类型转换
            Expr::BinaryExpr(BinaryExpr { left, op, right }) => {
                let (left, right) = self.cast_binary_expr_operands(*left, *right)?;
                Ok(Transformed::yes(Expr::BinaryExpr(BinaryExpr {
                    left: Box::new(left),
                    op,
                    right: Box::new(right),
                })))
            }
            _ => Ok(Transformed::no(expr)),
        }
    }
}

impl TypeConverter {
    /// 转换标量值到目标类型
    fn cast_scalar_value(
        &self,
        value: &ScalarValue,
        target_type: &DataType,
    ) -> Result<ScalarValue> {
        match (target_type, value) {
            // 字符串到时间戳的转换
            (DataType::Timestamp(_, _), ScalarValue::Utf8(Some(v))) => {
                string_to_timestamp_ms(v, Some(&self.query_ctx.timezone()))
            }
            // 字符串到布尔值的转换
            (DataType::Boolean, ScalarValue::Utf8(Some(v))) => match v.to_lowercase().as_str() {
                "true" => Ok(ScalarValue::Boolean(Some(true))),
                "false" => Ok(ScalarValue::Boolean(Some(false))),
                _ => Ok(ScalarValue::Boolean(None)),
            },
            // 其他类型转换使用 Arrow 的 cast 函数
            (target_type, value) => {
                let value_arr = value.to_array()?;
                let arr = compute::cast(&value_arr, target_type)?;
                ScalarValue::try_from_array(&arr, 0)
            }
        }
    }
}
````
</augment_code_snippet>

## 多语言查询支持

### 1. SQL 查询处理

- **方言支持**: GreptimeDB 自定义 SQL 方言
- **语法扩展**: 支持时间序列特定的语法
- **兼容性**: 与标准 SQL 保持兼容

### 2. PromQL 查询处理

- **PromQL 解析器**: 解析 Prometheus 查询语言
- **指标查询优化**: 针对时间序列数据的优化
- **函数支持**: 完整的 PromQL 函数库

### 3. 日志查询处理

- **日志查询语言**: 专门的日志查询语法
- **全文搜索**: 支持日志内容的全文搜索
- **时间范围过滤**: 高效的时间范围查询

## 性能优化特性

### 1. 查询并行化

- **分区并行**: 基于数据分区的并行执行
- **流水线并行**: 算子间的流水线并行
- **区域并行**: 跨区域的并行查询

### 2. 内存管理

- **流式处理**: 避免大数据集的内存积累
- **批处理优化**: 优化的批处理大小
- **内存池**: 高效的内存分配和回收

### 3. 缓存机制

- **计划缓存**: 缓存编译后的查询计划
- **元数据缓存**: 缓存表和模式信息
- **结果缓存**: 缓存查询结果（可选）

## 监控和指标

### 1. 查询指标

- **执行时间**: 各阶段的执行时间统计
- **资源使用**: CPU、内存、网络使用情况
- **错误统计**: 查询错误和失败统计

### 2. 分布式指标

- **区域查询**: 各区域的查询性能
- **网络传输**: 跨区域数据传输统计
- **负载均衡**: 查询负载分布情况

## 扩展性设计

### 1. 插件系统

- **物理计划包装器**: 自定义物理计划处理
- **函数扩展**: 自定义标量和聚合函数
- **优化规则扩展**: 自定义优化规则

### 2. 接口抽象

- **查询引擎接口**: 统一的查询引擎抽象
- **存储接口**: 抽象的存储访问接口
- **目录接口**: 抽象的元数据访问接口

## 代码实现细节

### 1. 查询执行器 (QueryExecutor)

查询执行器负责将物理计划转换为可执行的数据流：

<augment_code_snippet path="src/query/src/datafusion.rs" mode="EXCERPT">
````rust
impl QueryExecutor for DatafusionQueryEngine {
    fn execute_stream(
        &self,
        ctx: &QueryEngineContext,
        plan: &Arc<dyn ExecutionPlan>,
    ) -> Result<SendableRecordBatchStream> {
        let exec_timer = metrics::EXEC_PLAN_ELAPSED.start_timer();
        let task_ctx = ctx.build_task_ctx();

        match plan.properties().output_partitioning().partition_count() {
            0 => {
                // 空结果集处理
                let schema = Arc::new(Schema::try_from(plan.schema())?);
                Ok(Box::pin(EmptyRecordBatchStream::new(schema)))
            }
            1 => {
                // 单分区执行
                let df_stream = plan.execute(0, task_ctx)?;
                let mut stream = RecordBatchStreamAdapter::try_new(df_stream)?;
                stream.set_metrics2(plan.clone());
                Ok(Box::pin(OnDone::new(Box::pin(stream), move || {
                    exec_timer.observe_duration();
                })))
            }
            _ => {
                // 多分区合并执行
                let merged_plan = CoalescePartitionsExec::new(plan.clone());
                let df_stream = merged_plan.execute(0, task_ctx)?;
                let mut stream = RecordBatchStreamAdapter::try_new(df_stream)?;
                stream.set_metrics2(plan.clone());
                Ok(Box::pin(OnDone::new(Box::pin(stream), move || {
                    exec_timer.observe_duration();
                })))
            }
        }
    }
}
````
</augment_code_snippet>

### 2. DML 语句处理

查询引擎支持 INSERT 和 DELETE 操作的处理：

<augment_code_snippet path="src/query/src/datafusion.rs" mode="EXCERPT">
````rust
async fn exec_dml_statement(
    &self,
    dml: DmlStatement,
    query_ctx: QueryContextRef,
) -> Result<Output> {
    // 1. 验证 DML 操作类型
    ensure!(
        matches!(dml.op, WriteOp::Insert(_) | WriteOp::Delete),
        UnsupportedExprSnafu { name: format!("DML op {}", dml.op) }
    );

    // 2. 解析表名
    let table_name = dml.table_name.resolve(default_catalog, default_schema);
    let table = self.find_table(&table_name, &query_ctx).await?;

    // 3. 执行输入查询计划
    let output = self.exec_query_plan((*dml.input).clone(), query_ctx.clone()).await?;
    let mut stream = match output.data {
        OutputData::RecordBatches(batches) => batches.as_stream(),
        OutputData::Stream(stream) => stream,
        _ => unreachable!(),
    };

    // 4. 处理数据流并执行 DML 操作
    let mut affected_rows = 0;
    while let Some(batch) = stream.next().await {
        let batch = batch.context(CreateRecordBatchSnafu)?;
        let column_vectors = batch.column_vectors(&table_name.to_string(), table.schema())?;

        match dml.op {
            WriteOp::Insert(_) => {
                let output = self.insert(&table_name, column_vectors, query_ctx.clone()).await?;
                affected_rows += output.extract_rows_and_cost().0;
            }
            WriteOp::Delete => {
                affected_rows += self.delete(&table_name, &table, column_vectors, query_ctx.clone()).await?;
            }
            _ => unreachable!(),
        }
    }

    Ok(Output::new(OutputData::AffectedRows(affected_rows), OutputMeta::default()))
}
````
</augment_code_snippet>

### 3. 物理计划创建过程

物理计划的创建包含多个优化阶段：

<augment_code_snippet path="src/query/src/datafusion.rs" mode="EXCERPT">
````rust
async fn create_physical_plan(
    &self,
    ctx: &mut QueryEngineContext,
    logical_plan: &LogicalPlan,
) -> Result<Arc<dyn ExecutionPlan>> {
    let state = ctx.state();

    // 1. 特殊处理 EXPLAIN 计划
    if matches!(logical_plan, DfLogicalPlan::Explain(_)) {
        return state.create_physical_plan(logical_plan).await;
    }

    // 2. 分析阶段 - 验证和转换逻辑计划
    let analyzed_plan = state
        .analyzer()
        .execute_and_check(logical_plan.clone(), state.config_options(), |_, _| {})?;

    // 3. 逻辑优化阶段 - 跳过 MergeScan 的优化
    let optimized_plan = if let DfLogicalPlan::Extension(ext) = &analyzed_plan
        && ext.node.name() == MergeScanLogicalPlan::name()
    {
        analyzed_plan.clone()
    } else {
        state.optimizer().optimize(analyzed_plan, state, |_, _| {})?
    };

    // 4. 物理计划生成
    let physical_plan = state
        .query_planner()
        .create_physical_plan(&optimized_plan, state)
        .await?;

    Ok(physical_plan)
}
````
</augment_code_snippet>

### 4. 查询语言解析器

支持多种查询语言的统一解析接口：

<augment_code_snippet path="src/query/src/parser.rs" mode="EXCERPT">
````rust
#[derive(Debug, Clone)]
pub enum QueryStatement {
    Sql(Statement),
    Promql(EvalStmt),
}

impl QueryStatement {
    pub fn post_process(&self, params: HashMap<String, String>) -> Result<QueryStatement> {
        match self {
            QueryStatement::Sql(_) => UnimplementedSnafu {
                operation: "sql post process",
            }.fail(),
            QueryStatement::Promql(eval_stmt) => {
                let node_name = params.get("name").map(|s| s.as_str()).unwrap_or("");
                let extension_node = Self::create_extension_node(node_name, &eval_stmt.expr);
                Ok(QueryStatement::Promql(EvalStmt {
                    expr: Extension(extension_node.unwrap()),
                    start: eval_stmt.start,
                    end: eval_stmt.end,
                    interval: eval_stmt.interval,
                    lookback_delta: eval_stmt.lookback_delta,
                }))
            }
        }
    }
}
````
</augment_code_snippet>

## 高级特性分析

### 1. 时间范围查询优化

GreptimeDB 针对时间序列数据实现了专门的范围查询优化：

- **Range Plan Rewriter**: 重写包含时间范围的查询计划
- **时间索引利用**: 充分利用时间戳索引加速查询
- **分区裁剪**: 基于时间范围进行分区裁剪

### 2. 向量化执行

基于 Apache Arrow 的向量化执行引擎：

- **列式存储**: 高效的列式数据处理
- **SIMD 优化**: 利用 SIMD 指令加速计算
- **批处理**: 批量处理减少函数调用开销

### 3. 内存管理策略

- **流式处理**: 避免大数据集完全加载到内存
- **反压机制**: 防止内存溢出的反压控制
- **资源限制**: 可配置的内存和 CPU 使用限制

### 4. 错误处理和容错

- **分层错误处理**: 不同层次的错误处理策略
- **部分失败容错**: 分布式查询中的部分失败处理
- **重试机制**: 自动重试失败的操作

## 性能调优指南

### 1. 查询优化建议

- **索引使用**: 合理使用时间戳和标签索引
- **分区策略**: 基于时间和标签的分区策略
- **并行度调整**: 根据硬件配置调整查询并行度

### 2. 配置优化

- **内存配置**: 合理配置查询引擎内存限制
- **并发控制**: 控制并发查询数量
- **缓存策略**: 配置合适的缓存策略

### 3. 监控指标

- **查询延迟**: 监控查询响应时间
- **资源使用**: 监控 CPU、内存、网络使用
- **错误率**: 监控查询错误和失败率

## 扩展开发指南

### 1. 自定义函数开发

```rust
// 自定义标量函数示例
pub struct CustomFunction;

impl ScalarFunctionFactory for CustomFunction {
    fn name(&self) -> &str {
        "custom_func"
    }

    fn create(&self) -> Arc<dyn ScalarFunction> {
        Arc::new(CustomFunctionImpl)
    }
}
```

### 2. 自定义优化规则

```rust
// 自定义优化规则示例
pub struct CustomOptimizerRule;

impl ExtensionAnalyzerRule for CustomOptimizerRule {
    fn analyze(
        &self,
        plan: LogicalPlan,
        ctx: &QueryEngineContext,
        config: &ConfigOptions,
    ) -> Result<LogicalPlan> {
        // 实现自定义优化逻辑
        Ok(plan)
    }
}
```

### 3. 插件开发

```rust
// 物理计划包装器插件示例
pub struct CustomPlanWrapper;

impl PhysicalPlanWrapper for CustomPlanWrapper {
    fn wrap(
        &self,
        plan: Arc<dyn ExecutionPlan>,
        query_ctx: QueryContextRef,
    ) -> Arc<dyn ExecutionPlan> {
        // 实现自定义包装逻辑
        plan
    }
}
```

## 总结

GreptimeDB 的查询引擎是一个设计精良的分布式查询处理系统，具有以下核心优势：

1. **高性能**: 基于 DataFusion 的向量化执行和并行处理
2. **分布式**: 原生支持分布式查询和数据处理
3. **多语言**: 支持 SQL、PromQL 等多种查询语言
4. **可扩展**: 灵活的插件系统和扩展机制
5. **优化**: 丰富的查询优化规则和策略
6. **容错**: 完善的错误处理和容错机制
7. **监控**: 全面的性能监控和指标收集

该架构为时间序列数据库提供了强大的查询处理能力，能够高效处理大规模时间序列数据的复杂查询需求，同时保持良好的扩展性和可维护性。
