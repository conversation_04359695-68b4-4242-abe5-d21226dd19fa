---
name: Bug report
description: Is something not working? Help us fix it!
labels: [ "C-bug" ]
body:
  - type: markdown
    attributes:
      value: |
        Take some time to fill out this bug report. Thank you!

  - type: dropdown
    id: type
    attributes:
      label: What type of bug is this?
      multiple: true
      options:
        - Configuration
        - Crash
        - Data corruption
        - Incorrect result
        - Locking issue
        - Performance issue
        - Unexpected error
        - User Experience
        - Other
    validations:
      required: true

  - type: dropdown
    id: subsystem
    attributes:
      label: What subsystems are affected?
      description: You can pick multiple subsystems.
      multiple: true
      options:
        - Standalone mode
        - Distributed Cluster
        - Storage Engine
        - Query Engine
        - Table Engine
        - Write Protocols
        - Metasrv
        - Frontend
        - Datanode
        - Other
    validations:
      required: true

  - type: textarea
    id: reproduce
    attributes:
      label: Minimal reproduce step
      description: |
        Please walk us through and provide steps and details on how
        to reproduce the issue. If possible, provide scripts that we
        can run to trigger the bug.
    validations:
      required: true

  - type: textarea
    id: expected-manner
    attributes:
      label: What did you expect to see?
    validations:
      required: true

  - type: textarea
    id: actual-manner
    attributes:
      label: What did you see instead?
    validations:
      required: true

  - type: input
    id: os
    attributes:
      label: What operating system did you use?
      description: |
        Please provide OS, version, and architecture. For example:
        Windows 10 x64, Ubuntu 21.04 x64, Mac OS X 10.5 ARM, Rasperry
        Pi i386, etc.
      placeholder: "Ubuntu 21.04 x64"
    validations:
      required: true

  - type: input
    id: greptimedb
    attributes:
      label: What version of GreptimeDB did you use?
      description: |
        Please provide the version of GreptimeDB. For example:
        0.5.1 etc. You can get it by executing command line `greptime --version`.
      placeholder: "0.5.1"
    validations:
      required: true

  - type: textarea
    id: logs
    attributes:
      label: Relevant log output and stack trace
      description: |
        Please copy and paste any relevant log output or a stack
        trace. This will be automatically formatted into code, so no
        need for backticks.
      render: bash
