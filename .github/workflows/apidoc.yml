on:
  push:
    branches:
      - main
    paths-ignore:
      - 'docs/**'
      - 'config/**'
      - '**.md'
      - '.dockerignore'
      - 'docker/**'
      - '.gitignore'

name: Build API docs

jobs:
  apidoc:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        persist-credentials: false
    - uses: arduino/setup-protoc@v3
      with:
        repo-token: ${{ secrets.GITHUB_TOKEN }}
    - uses: actions-rust-lang/setup-rust-toolchain@v1
    - run: cargo doc --workspace --no-deps --document-private-items
    - run: |
        cat <<EOF > target/doc/index.html
        <!DOCTYPE html>
        <html>
        <head>
        <meta http-equiv="refresh" content="0; url='greptime/'" />
        </head>
        <body></body></html>
        EOF
    - name: Publish dist directory
      uses: JamesIves/github-pages-deploy-action@v4
      with:
        folder: target/doc
        single-commit: true
