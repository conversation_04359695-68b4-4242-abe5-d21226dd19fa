name: Release dev-builder images

on:
  push:
    branches:
      - main
    paths:
      - rust-toolchain.toml
      - 'docker/dev-builder/**'
  workflow_dispatch: # Allows you to run this workflow manually.
    inputs:
      release_dev_builder_ubuntu_image:
        type: boolean
        description: Release dev-builder-ubuntu image
        required: false
        default: false
      release_dev_builder_centos_image:
        type: boolean
        description: Release dev-builder-centos image
        required: false
        default: false
      release_dev_builder_android_image:
        type: boolean
        description: Release dev-builder-android image
        required: false
        default: false
      update_dev_builder_image_tag:
        type: boolean
        description: Update the DEV_BUILDER_IMAGE_TAG in Makefile and create a PR
        required: false
        default: false

jobs:
  release-dev-builder-images:
    name: Release dev builder images
    # The jobs are triggered by the following events:
    # 1. Manually triggered workflow_dispatch event
    # 2. Push event when the PR that modifies the `rust-toolchain.toml` or `docker/dev-builder/**` is merged to main
    if: ${{ github.event_name == 'push' || inputs.release_dev_builder_ubuntu_image || inputs.release_dev_builder_centos_image || inputs.release_dev_builder_android_image }}
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.set-version.outputs.version }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          persist-credentials: false

      - name: Configure build image version
        id: set-version
        shell: bash
        run: |
          commitShortSHA=`echo ${{ github.sha }} | cut -c1-8`
          buildTime=`date +%Y%m%d%H%M%S`
          BUILD_VERSION="$commitShortSHA-$buildTime"
          RUST_TOOLCHAIN_VERSION=$(cat rust-toolchain.toml | grep -Eo '[0-9]{4}-[0-9]{2}-[0-9]{2}')
          IMAGE_VERSION="${RUST_TOOLCHAIN_VERSION}-${BUILD_VERSION}"
          echo "VERSION=${IMAGE_VERSION}" >> $GITHUB_ENV
          echo "version=$IMAGE_VERSION" >> $GITHUB_OUTPUT

      - name: Build and push dev builder images
        uses: ./.github/actions/build-dev-builder-images
        with:
          version: ${{ env.VERSION }}
          dockerhub-image-registry-username: ${{ secrets.DOCKERHUB_USERNAME }}
          dockerhub-image-registry-token: ${{ secrets.DOCKERHUB_TOKEN }}
          build-dev-builder-ubuntu: ${{ inputs.release_dev_builder_ubuntu_image || github.event_name == 'push' }}
          build-dev-builder-centos: ${{ inputs.release_dev_builder_centos_image || github.event_name == 'push' }}
          build-dev-builder-android: ${{ inputs.release_dev_builder_android_image || github.event_name == 'push' }}

  release-dev-builder-images-ecr:
    name: Release dev builder images to AWS ECR
    runs-on: ubuntu-latest
    needs: [
      release-dev-builder-images
    ]
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ECR_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_ECR_SECRET_ACCESS_KEY }}
          aws-region: ${{ vars.ECR_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr-public
        uses: aws-actions/amazon-ecr-login@v2
        env:
          AWS_REGION: ${{ vars.ECR_REGION }}
        with:
          registry-type: public

      - name: Push dev-builder-ubuntu image
        shell: bash
        if: ${{ inputs.release_dev_builder_ubuntu_image || github.event_name == 'push' }}
        env:
          IMAGE_VERSION: ${{ needs.release-dev-builder-images.outputs.version }}
          IMAGE_NAMESPACE: ${{ vars.IMAGE_NAMESPACE }}
          ECR_IMAGE_REGISTRY: ${{ vars.ECR_IMAGE_REGISTRY }}
          ECR_IMAGE_NAMESPACE: ${{ vars.ECR_IMAGE_NAMESPACE }}
        run: |
          docker run -v "${DOCKER_CONFIG:-$HOME/.docker}:/root/.docker:ro" \
            -e "REGISTRY_AUTH_FILE=/root/.docker/config.json" \
            quay.io/skopeo/stable:latest \
            copy -a docker://docker.io/$IMAGE_NAMESPACE/dev-builder-ubuntu:$IMAGE_VERSION \
            docker://$ECR_IMAGE_REGISTRY/$ECR_IMAGE_NAMESPACE/dev-builder-ubuntu:$IMAGE_VERSION

          docker run -v "${DOCKER_CONFIG:-$HOME/.docker}:/root/.docker:ro" \
            -e "REGISTRY_AUTH_FILE=/root/.docker/config.json" \
            quay.io/skopeo/stable:latest \
            copy -a docker://docker.io/$IMAGE_NAMESPACE/dev-builder-ubuntu:latest \
            docker://$ECR_IMAGE_REGISTRY/$ECR_IMAGE_NAMESPACE/dev-builder-ubuntu:latest

      - name: Push dev-builder-centos image
        shell: bash
        if: ${{ inputs.release_dev_builder_centos_image || github.event_name == 'push' }}
        env:
          IMAGE_VERSION: ${{ needs.release-dev-builder-images.outputs.version }}
          IMAGE_NAMESPACE: ${{ vars.IMAGE_NAMESPACE }}
          ECR_IMAGE_REGISTRY: ${{ vars.ECR_IMAGE_REGISTRY }}
          ECR_IMAGE_NAMESPACE: ${{ vars.ECR_IMAGE_NAMESPACE }}
        run: |
          docker run -v "${DOCKER_CONFIG:-$HOME/.docker}:/root/.docker:ro" \
            -e "REGISTRY_AUTH_FILE=/root/.docker/config.json" \
            quay.io/skopeo/stable:latest \
            copy -a docker://docker.io/$IMAGE_NAMESPACE/dev-builder-centos:$IMAGE_VERSION \
            docker://$ECR_IMAGE_REGISTRY/$ECR_IMAGE_NAMESPACE/dev-builder-centos:$IMAGE_VERSION

          docker run -v "${DOCKER_CONFIG:-$HOME/.docker}:/root/.docker:ro" \
            -e "REGISTRY_AUTH_FILE=/root/.docker/config.json" \
            quay.io/skopeo/stable:latest \
            copy -a docker://docker.io/$IMAGE_NAMESPACE/dev-builder-centos:latest \
            docker://$ECR_IMAGE_REGISTRY/$ECR_IMAGE_NAMESPACE/dev-builder-centos:latest

      - name: Push dev-builder-android image
        shell: bash
        if: ${{ inputs.release_dev_builder_android_image || github.event_name == 'push' }}
        env:
          IMAGE_VERSION: ${{ needs.release-dev-builder-images.outputs.version }}
          IMAGE_NAMESPACE: ${{ vars.IMAGE_NAMESPACE }}
          ECR_IMAGE_REGISTRY: ${{ vars.ECR_IMAGE_REGISTRY }}
          ECR_IMAGE_NAMESPACE: ${{ vars.ECR_IMAGE_NAMESPACE }}
        run: |
          docker run -v "${DOCKER_CONFIG:-$HOME/.docker}:/root/.docker:ro" \
            -e "REGISTRY_AUTH_FILE=/root/.docker/config.json" \
            quay.io/skopeo/stable:latest \
            copy -a docker://docker.io/$IMAGE_NAMESPACE/dev-builder-android:$IMAGE_VERSION \
            docker://$ECR_IMAGE_REGISTRY/$ECR_IMAGE_NAMESPACE/dev-builder-android:$IMAGE_VERSION

          docker run -v "${DOCKER_CONFIG:-$HOME/.docker}:/root/.docker:ro" \
            -e "REGISTRY_AUTH_FILE=/root/.docker/config.json" \
            quay.io/skopeo/stable:latest \
            copy -a docker://docker.io/$IMAGE_NAMESPACE/dev-builder-android:latest \
            docker://$ECR_IMAGE_REGISTRY/$ECR_IMAGE_NAMESPACE/dev-builder-android:latest

  release-dev-builder-images-cn: # Note: Be careful issue: https://github.com/containers/skopeo/issues/1874 and we decide to use the latest stable skopeo container.
    name: Release dev builder images to CN region
    runs-on: ubuntu-latest
    needs: [
      release-dev-builder-images
    ]
    steps:
      - name: Login to AliCloud Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ vars.ACR_IMAGE_REGISTRY }}
          username: ${{ secrets.ALICLOUD_USERNAME }}
          password: ${{ secrets.ALICLOUD_PASSWORD }}

      - name: Push dev-builder-ubuntu image
        shell: bash
        if: ${{ inputs.release_dev_builder_ubuntu_image || github.event_name == 'push' }}
        env:
          IMAGE_VERSION: ${{ needs.release-dev-builder-images.outputs.version }}
          IMAGE_NAMESPACE: ${{ vars.IMAGE_NAMESPACE }}
          ACR_IMAGE_REGISTRY: ${{ vars.ACR_IMAGE_REGISTRY }}
        run: |
          docker run -v "${DOCKER_CONFIG:-$HOME/.docker}:/root/.docker:ro" \
            -e "REGISTRY_AUTH_FILE=/root/.docker/config.json" \
            quay.io/skopeo/stable:latest \
            copy -a docker://docker.io/$IMAGE_NAMESPACE/dev-builder-ubuntu:$IMAGE_VERSION \
            docker://$ACR_IMAGE_REGISTRY/$IMAGE_NAMESPACE/dev-builder-ubuntu:$IMAGE_VERSION

      - name: Push dev-builder-centos image
        shell: bash
        if: ${{ inputs.release_dev_builder_centos_image || github.event_name == 'push' }}
        env:
          IMAGE_VERSION: ${{ needs.release-dev-builder-images.outputs.version }}
          IMAGE_NAMESPACE: ${{ vars.IMAGE_NAMESPACE }}
          ACR_IMAGE_REGISTRY: ${{ vars.ACR_IMAGE_REGISTRY }}
        run: |
          docker run -v "${DOCKER_CONFIG:-$HOME/.docker}:/root/.docker:ro" \
            -e "REGISTRY_AUTH_FILE=/root/.docker/config.json" \
            quay.io/skopeo/stable:latest \
            copy -a docker://docker.io/$IMAGE_NAMESPACE/dev-builder-centos:$IMAGE_VERSION \
            docker://$ACR_IMAGE_REGISTRY/$IMAGE_NAMESPACE/dev-builder-centos:$IMAGE_VERSION

      - name: Push dev-builder-android image
        shell: bash
        if: ${{ inputs.release_dev_builder_android_image || github.event_name == 'push' }}
        env:
          IMAGE_VERSION: ${{ needs.release-dev-builder-images.outputs.version }}
          IMAGE_NAMESPACE: ${{ vars.IMAGE_NAMESPACE }}
          ACR_IMAGE_REGISTRY: ${{ vars.ACR_IMAGE_REGISTRY }}
        run: |
          docker run -v "${DOCKER_CONFIG:-$HOME/.docker}:/root/.docker:ro" \
            -e "REGISTRY_AUTH_FILE=/root/.docker/config.json" \
            quay.io/skopeo/stable:latest \
            copy -a docker://docker.io/$IMAGE_NAMESPACE/dev-builder-android:$IMAGE_VERSION \
            docker://$ACR_IMAGE_REGISTRY/$IMAGE_NAMESPACE/dev-builder-android:$IMAGE_VERSION
  
  update-dev-builder-image-tag:
    name: Update dev-builder image tag
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write
    if: ${{ github.event_name == 'push' || inputs.update_dev_builder_image_tag }}
    needs: [
      release-dev-builder-images
    ]
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Update dev-builder image tag
        shell: bash
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          ./.github/scripts/update-dev-builder-version.sh ${{ needs.release-dev-builder-images.outputs.version }}
