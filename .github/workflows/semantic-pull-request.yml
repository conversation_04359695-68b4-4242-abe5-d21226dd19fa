name: "Semantic Pull Request"

on:
  pull_request:
    types:
      - opened
      - reopened
      - edited

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

permissions:
  issues: write
  contents: write
  pull-requests: write

jobs:
  check:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup-cyborg
      - name: Check Pull Request
        working-directory: cyborg
        run: pnpm tsx bin/check-pull-request.ts
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
