name: 'PR Labeling'

on:
  pull_request_target:
    types:
      - opened
      - synchronize
      - reopened

permissions:
  contents: read
  pull-requests: write
  issues: write

jobs:
  labeler:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout sources
        uses: actions/checkout@v4

      - uses: actions/labeler@v5
        with:
          configuration-path: ".github/labeler.yaml"
          repo-token: "${{ secrets.GITHUB_TOKEN }}"

  size-label:
    runs-on: ubuntu-latest
    steps:
      - uses: pascalgn/size-label-action@v0.5.5
        env:
          GITHUB_TOKEN: "${{ secrets.GITHUB_TOKEN }}"
        with:
          sizes: >
            {
              "0": "XS",
              "100": "S",
              "300": "M",
              "1000": "L",
              "1500": "XL",
              "2000": "XXL"
            }
