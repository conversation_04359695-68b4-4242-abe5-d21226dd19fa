meta:
  configData: |-
    [runtime]
    global_rt_size = 4

    [datanode]
    [datanode.client]
    timeout = "120s"
datanode:
  configData: |-
    [runtime]
    global_rt_size = 4
    compact_rt_size = 2
    
    [storage]
    cache_path = "/data/greptimedb/s3cache"
    cache_capacity = "256MB"
frontend:
  configData: |-
    [runtime]
    global_rt_size = 4

    [meta_client]
    ddl_timeout = "120s"
objectStorage:
  s3:
    bucket: default
    region: us-west-2
    root: test-root
    endpoint: http://minio.minio.svc.cluster.local 
  credentials:
    accessKeyId: rootuser
    secretAccessKey: rootpass123
