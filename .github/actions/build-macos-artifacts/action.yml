name: Build macos artifacts
description: Build macos artifacts
inputs:
  arch:
    description: Architecture to build
    required: true
  cargo-profile:
    description: Cargo profile to build
    required: true
  features:
    description: Cargo features to build
    required: true
  version:
    description: Version of the artifact
    required: true
  disable-run-tests:
    description: Disable running integration tests
    required: true
  artifacts-dir:
    description: Directory to store artifacts
    required: true
runs:
  using: composite
  steps:
    - name: Cache cargo assets
      id: cache
      uses: actions/cache@v3
      with:
        path: |
          ~/.cargo/bin/
          ~/.cargo/registry/index/
          ~/.cargo/registry/cache/
          ~/.cargo/git/db/
          target/
        key: ${{ inputs.arch }}-build-cargo-${{ hashFiles('**/Cargo.lock') }}

    - name: Install protoc
      shell: bash
      run: |
        brew install protobuf

    - name: Install rust toolchain
      uses: actions-rust-lang/setup-rust-toolchain@v1
      with:
        target: ${{ inputs.arch }}

    - name: Start etcd # For integration tests.
      if: ${{ inputs.disable-run-tests == 'false' }}
      shell: bash
      run: |
        brew install etcd && \
        brew services start etcd

    - name: Install latest nextest release # For integration tests.
      if: ${{ inputs.disable-run-tests == 'false' }}
      uses: taiki-e/install-action@nextest

    # Get proper backtraces in mac Sonoma. Currently there's an issue with the new
    # linker that prevents backtraces from getting printed correctly.
    #
    # <https://github.com/rust-lang/rust/issues/113783>
    - name: Run integration tests
      if: ${{ inputs.disable-run-tests == 'false' }}
      shell: bash
      env:
        CARGO_BUILD_RUSTFLAGS: "-Clink-arg=-Wl,-ld_classic"
        SQLNESS_OPTS: "--preserve-state"
      run: |
        make test sqlness-test

    - name: Upload sqlness logs
      if: ${{ failure() }} # Only upload logs when the integration tests failed.
      uses: actions/upload-artifact@v4
      with:
        name: sqlness-logs
        path: /tmp/greptime-*.log
        retention-days: 3

    - name: Build greptime binary
      shell: bash
      env:
        CARGO_BUILD_RUSTFLAGS: "-Clink-arg=-Wl,-ld_classic"
      run: |
        make build \
        CARGO_PROFILE=${{ inputs.cargo-profile }} \
        FEATURES=${{ inputs.features }} \
        TARGET=${{ inputs.arch }}

    - name: Upload artifacts
      uses: ./.github/actions/upload-artifacts
      with:
        artifacts-dir: ${{ inputs.artifacts-dir }}
        target-files: target/${{ inputs.arch }}/${{ inputs.cargo-profile }}/greptime
        version: ${{ inputs.version }}
