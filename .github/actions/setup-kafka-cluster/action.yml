name: Setup Kafka cluster
description: Deploy Kafka cluster on Kubernetes
inputs:
  controller-replicas:
    default: 3
    description: "Kafka controller replicas"
  namespace:
    default: "kafka-cluster"

runs:
  using: composite
  steps:
  - name: Install Kafka cluster
    shell: bash
    run: | 
      helm upgrade \
        --install kafka oci://registry-1.docker.io/bitnamicharts/kafka \
        --set controller.replicaCount=${{ inputs.controller-replicas }} \
        --set controller.resources.requests.cpu=50m \
        --set controller.resources.requests.memory=128Mi \
        --set controller.resources.limits.cpu=2000m \
        --set controller.resources.limits.memory=2Gi \
        --set listeners.controller.protocol=PLAINTEXT \
        --set listeners.client.protocol=PLAINTEXT \
        --create-namespace \
        -n ${{ inputs.namespace }}
