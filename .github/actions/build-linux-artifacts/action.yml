name: Build linux artifacts
description: Build linux artifacts
inputs:
  arch:
    description: Architecture to build
    required: true
  cargo-profile:
    description: Cargo profile to build
    required: true
  version:
    description: Version of the artifact
    required: true
  disable-run-tests:
    description: Disable running integration tests
    required: true
  dev-mode:
    description: Enable dev mode, only build standard greptime
    required: false
    default: "false"
  image-namespace:
    description: Image Namespace
    required: true
  image-registry:
    description: Image Registry
    required: true
  working-dir:
    description: Working directory to build the artifacts
    required: false
    default: .
runs:
  using: composite
  steps:
    - name: Run integration test
      if: ${{ inputs.disable-run-tests == 'false' }}
      shell: bash
      # NOTE: If the BUILD_JOBS > 4, it's always OOM in EC2 instance.
      run: |
        cd ${{ inputs.working-dir }} && \
        make run-it-in-container BUILD_JOBS=4 \
        IMAGE_NAMESPACE=${{ inputs.image-namespace }} \
        IMAGE_REGISTRY=${{ inputs.image-registry }}

    - name: Upload sqlness logs
      if: ${{ failure() && inputs.disable-run-tests == 'false' }} # Only upload logs when the integration tests failed.
      uses: actions/upload-artifact@v4
      with:
        name: sqlness-logs
        path: /tmp/greptime-*.log
        retention-days: 3

    - name: Build greptime # Builds standard greptime binary
      uses: ./.github/actions/build-greptime-binary
      with:
        base-image: ubuntu
        features: servers/dashboard
        cargo-profile: ${{ inputs.cargo-profile }}
        artifacts-dir: greptime-linux-${{ inputs.arch }}-${{ inputs.version }}
        version: ${{ inputs.version }}
        working-dir: ${{ inputs.working-dir }}
        image-registry: ${{ inputs.image-registry }}
        image-namespace: ${{ inputs.image-namespace }}

    - name: Clean up the target directory # Clean up the target directory for the centos7 base image, or it will still use the objects of last build.
      shell: bash
      run: |
        rm -rf ./target/

    - name: Build greptime on centos base image
      uses: ./.github/actions/build-greptime-binary
      if: ${{ inputs.arch == 'amd64' && inputs.dev-mode == 'false' }} # Builds greptime for centos if the host machine is amd64.
      with:
        base-image: centos
        features: servers/dashboard
        cargo-profile: ${{ inputs.cargo-profile }}
        artifacts-dir: greptime-linux-${{ inputs.arch }}-centos-${{ inputs.version }}
        version: ${{ inputs.version }}
        working-dir: ${{ inputs.working-dir }}
        image-registry: ${{ inputs.image-registry }}
        image-namespace: ${{ inputs.image-namespace }}

    - name: Build greptime on android base image
      uses: ./.github/actions/build-greptime-binary
      if: ${{ inputs.arch == 'amd64' && inputs.dev-mode == 'false' }} # Builds arm64 greptime binary for android if the host machine amd64.
      with:
        base-image: android
        artifacts-dir: greptime-android-arm64-${{ inputs.version }}
        version: ${{ inputs.version }}
        working-dir: ${{ inputs.working-dir }}
        build-android-artifacts: true
        image-registry: ${{ inputs.image-registry }}
        image-namespace: ${{ inputs.image-namespace }}
