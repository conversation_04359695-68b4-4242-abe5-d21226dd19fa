name: Build greptime images
description: Build and push greptime images
inputs:
  image-registry:
    description: The image registry to store the images
    required: true
  image-registry-username:
    description: The username to login to the image registry
    required: true
  image-registry-password:
    description: The password to login to the image registry
    required: true
  amd64-artifact-name:
    description: The name of the amd64 artifact for building images
    required: true
  arm64-artifact-name:
    description: The name of the arm64 artifact for building images
    required: false
    default: ""
  image-namespace:
    description: The namespace of the image registry to store the images
    required: true
  image-name:
    description: The name of the image to build
    required: true
  image-tag:
    description: The tag of the image to build
    required: true
  docker-file:
    description: The path to the Dockerfile to build
    required: true
  platforms:
    description: The supported platforms to build the image
    required: true
  push-latest-tag:
    description: Whether to push the latest tag
    required: true
    default: 'false'
runs:
  using: composite
  steps:
    - name: Login to image registry
      uses: docker/login-action@v2
      with:
        registry: ${{ inputs.image-registry }}
        username: ${{ inputs.image-registry-username }}
        password: ${{ inputs.image-registry-password }}

    - name: Set up qemu for multi-platform builds
      uses: docker/setup-qemu-action@v3
      with:
        platforms: linux/amd64,linux/arm64
        # The latest version will lead to segmentation fault.
        image: tonistiigi/binfmt:qemu-v7.0.0-28

    - name: Set up buildx
      uses: docker/setup-buildx-action@v2

    - name: Download amd64 artifacts
      uses: actions/download-artifact@v4
      with:
        name: ${{ inputs.amd64-artifact-name }}

    - name: Unzip the amd64 artifacts
      shell: bash
      run: |
        tar xvf ${{ inputs.amd64-artifact-name }}.tar.gz && \
        rm ${{ inputs.amd64-artifact-name }}.tar.gz && \
        rm -rf amd64 && \
        mv ${{ inputs.amd64-artifact-name }} amd64

    - name: Download arm64 artifacts
      uses: actions/download-artifact@v4
      if: ${{ inputs.arm64-artifact-name }}
      with:
        name: ${{ inputs.arm64-artifact-name }}

    - name: Unzip the arm64 artifacts
      shell: bash
      if: ${{ inputs.arm64-artifact-name }}
      run: |
        tar xvf ${{ inputs.arm64-artifact-name }}.tar.gz && \
        rm ${{ inputs.arm64-artifact-name }}.tar.gz && \
        rm -rf arm64 && \
        mv ${{ inputs.arm64-artifact-name }} arm64

    - name: Build and push images(without latest) for amd64 and arm64
      if: ${{ inputs.push-latest-tag == 'false' }}
      uses: docker/build-push-action@v3
      with:
        context: .
        file: ${{ inputs.docker-file }}
        push: true
        platforms: ${{ inputs.platforms }}
        tags: |
          ${{ inputs.image-registry }}/${{ inputs.image-namespace }}/${{ inputs.image-name }}:${{ inputs.image-tag }}

    - name: Build and push images for amd64 and arm64
      if: ${{ inputs.push-latest-tag == 'true' }}
      uses: docker/build-push-action@v3
      with:
        context: .
        file: ${{ inputs.docker-file }}
        push: true
        platforms: ${{ inputs.platforms }}
        tags: |
          ${{ inputs.image-registry }}/${{ inputs.image-namespace }}/${{ inputs.image-name }}:latest
          ${{ inputs.image-registry }}/${{ inputs.image-namespace }}/${{ inputs.image-name }}:${{ inputs.image-tag }}
