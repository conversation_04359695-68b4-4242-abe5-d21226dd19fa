name: Fuzz Test
description: 'Fuzz test given setup and service'
inputs:
  target:
    description: "The fuzz target to test"
    required: true
  max-total-time:
    description: "Max total time(secs)"
    required: true
  unstable:
    default: 'false'
    description: "Enable unstable feature"
runs:
  using: composite
  steps:
  - name: Run Fuzz Test
    shell: bash
    run: cargo fuzz run ${{ inputs.target }} --fuzz-dir tests-fuzz -D -s none ${{ inputs.unstable == 'true' && '--features=unstable' || '' }} -- -max_total_time=${{ inputs.max-total-time }}

