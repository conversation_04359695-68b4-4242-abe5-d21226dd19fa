name: Build and push CI Docker image
description: Build and push CI Docker image to local registry
inputs:
  binary_path:
    default: "./bin"
    description: "Binary path"
runs:
  using: composite
  steps: 
    - name: Build and push to local registry
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./docker/ci/ubuntu/Dockerfile.fuzztests
        push: true
        tags: localhost:5001/greptime/greptimedb:latest
        build-args: |
          BINARY_PATH=${{ inputs.binary_path }}
