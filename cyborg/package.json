{"name": "cyborg", "version": "1.0.0", "description": "Automator for GreptimeDB Repository Management", "private": true, "packageManager": "pnpm@8.15.5", "dependencies": {"@actions/core": "^1.10.1", "@actions/github": "^6.0.0", "@octokit/request-error": "^6.1.1", "@octokit/webhooks-types": "^7.5.1", "conventional-commit-types": "^3.0.0", "conventional-commits-parser": "^5.0.0", "dayjs": "^1.11.11", "dotenv": "^16.4.5", "lodash": "^4.17.21"}, "devDependencies": {"@types/conventional-commits-parser": "^5.0.0", "@types/lodash": "^4.17.0", "@types/node": "^20.12.7", "tsconfig-paths": "^4.2.0", "tsx": "^4.8.2", "typescript": "^5.4.5"}}