// Copyright 2023 Greptime Team
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

use api::v1::{ColumnDataType, ColumnDef, SemanticType};
use derive_builder::Builder;

#[derive(Default, Builder)]
pub struct TestColumnDef {
    #[builder(setter(into), default)]
    name: String,
    data_type: ColumnDataType,
    #[builder(default)]
    is_nullable: bool,
    semantic_type: SemanticType,
    #[builder(setter(into), default)]
    comment: String,
}

impl From<TestColumnDef> for ColumnDef {
    fn from(
        TestColumnDef {
            name,
            data_type,
            is_nullable,
            semantic_type,
            comment,
        }: TestColumnDef,
    ) -> Self {
        Self {
            name,
            data_type: data_type as i32,
            is_nullable,
            default_constraint: vec![],
            semantic_type: semantic_type as i32,
            comment,
            datatype_extension: None,
            options: None,
        }
    }
}
