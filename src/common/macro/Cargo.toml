[package]
name = "common-macro"
version.workspace = true
edition.workspace = true
license.workspace = true

[lib]
proc-macro = true

[lints]
workspace = true

[dependencies]
proc-macro2 = "1.0.66"
quote = "1.0"
syn = { version = "2.0", features = [
    "extra-traits",
    "full",
] }

[dev-dependencies]
arc-swap = "1.0"
common-query.workspace = true
datatypes.workspace = true
snafu.workspace = true
static_assertions = "1.1.0"
