[package]
name = "auth"
version.workspace = true
edition.workspace = true
license.workspace = true

[features]
default = []
testing = []

[lints]
workspace = true

[dependencies]
api.workspace = true
async-trait.workspace = true
common-base.workspace = true
common-error.workspace = true
common-macro.workspace = true
common-telemetry.workspace = true
digest = "0.10"
notify.workspace = true
sha1 = "0.10"
snafu.workspace = true
sql.workspace = true
tokio.workspace = true

[dev-dependencies]
common-test-util.workspace = true
